# Discord RPC Settings

Qt6ベースのDiscord Rich Presence設定アプリケーションです。Discord Game SDKを使用してDiscordのリッチプレゼンス機能を管理できます。

## 機能

- **Discord接続管理**: Application IDを使用してDiscordに接続
- **リッチプレゼンス設定**: 状態、詳細、画像などを設定
- **タイムスタンプ機能**: 経過時間、残り時間、カスタム時間の設定
- **設定の保存/読み込み**: 設定を永続化
- **システムトレイ統合**: バックグラウンドで動作
- **直感的なUI**: Qt6による現代的なインターフェース

## 必要な環境

- NixOS または Nix パッケージマネージャー
- Qt6 (widgets, core, gui)
- Discord Game SDK
- xmake ビルドシステム

## ビルド方法

### NixOS / Nix Flakes を使用する場合

1. **開発環境に入る**:
   ```bash
   cd DiscordRPC-ReGen
   nix develop
   ```

2. **プロジェクトの設定とビルド**:
   ```bash
   xmake f -p linux -a x86_64 -m release
   xmake
   ```

3. **実行**:
   ```bash
   xmake run
   ```

4. **パッケージとしてビルド**:
   ```bash
   nix build
   ```

### 従来のNixを使用する場合

1. **開発環境に入る**:
   ```bash
   cd DiscordRPC-ReGen
   nix-shell
   ```

2. **プロジェクトの設定とビルド**:
   ```bash
   xmake f -p linux -a x86_64 -m release
   xmake
   ```

3. **実行**:
   ```bash
   xmake run
   ```

### 手動インストール（他のLinuxディストリビューション）

1. **依存関係のインストール**:
   ```bash
   # Ubuntu/Debian
   sudo apt install qt6-base-dev qt6-tools-dev build-essential

   # Arch Linux
   sudo pacman -S qt6-base qt6-tools base-devel

   # xmakeのインストール
   curl -fsSL https://xmake.io/shget.text | bash
   ```

2. **プロジェクトの設定**:
   ```bash
   cd DiscordRPC-ReGen
   xmake f -p linux -a x86_64 -m release
   ```

3. **ビルド**:
   ```bash
   xmake
   ```

4. **実行**:
   ```bash
   xmake run
   ```

## 使用方法

### 1. Discord Application の設定

1. [Discord Developer Portal](https://discord.com/developers/applications) にアクセス
2. 新しいアプリケーションを作成
3. Application ID をコピー

### 2. アプリケーションの使用

1. **接続設定**:
   - Application ID を入力
   - "Connect" ボタンをクリック

2. **リッチプレゼンスの設定**:
   - **State**: 現在の状態（例: "In Game", "Browsing Menu"）
   - **Details**: 詳細情報（例: "Playing Solo", "Level 5"）
   - **Large Image Key**: 大きな画像のアセットキー
   - **Large Image Text**: 大きな画像のホバーテキスト
   - **Small Image Key**: 小さな画像のアセットキー
   - **Small Image Text**: 小さな画像のホバーテキスト

3. **タイムスタンプ設定**:
   - **Start Time (Elapsed)**: 開始時刻から経過時間を表示
   - **End Time (Remaining)**: 終了時刻までの残り時間を表示
   - **Custom Time**: カスタム時刻を設定

4. **操作**:
   - "Update Activity": リッチプレゼンスを更新
   - "Clear Activity": リッチプレゼンスをクリア
   - "Save Settings": 設定を保存
   - "Load Settings": 設定を読み込み

### 3. システムトレイ

- アプリケーションを閉じるとシステムトレイに最小化
- トレイアイコンをダブルクリックで表示/非表示切り替え
- 右クリックでコンテキストメニュー

## ファイル構成

```
DiscordRPC-ReGen/
├── src/
│   ├── main.cpp              # メインエントリーポイント
│   ├── settings_window.h     # メインウィンドウヘッダー
│   ├── settings_window.cpp   # メインウィンドウ実装
│   ├── discord_manager.h     # Discord SDK ラッパーヘッダー
│   └── discord_manager.cpp   # Discord SDK ラッパー実装
├── third_party/
│   └── discord_game_sdk/     # Discord Game SDK
│       ├── include/
│       └── lib/
├── xmake.lua                 # ビルド設定
└── README.md                 # このファイル
```

## トラブルシューティング

### 接続できない場合

1. Discordが起動していることを確認
2. Application IDが正しいことを確認
3. Discord Game SDKの共有ライブラリが正しく配置されていることを確認
4. NixOSの場合、`LD_LIBRARY_PATH`が正しく設定されていることを確認

### ビルドエラーの場合

1. **NixOS環境の場合**:
   ```bash
   # 開発環境を再構築
   nix develop --rebuild
   # または
   nix-shell --pure
   ```

2. **Qt6が見つからない場合**:
   ```bash
   # 環境変数を確認
   echo $PKG_CONFIG_PATH
   echo $CMAKE_PREFIX_PATH

   # xmakeの設定をクリア
   xmake clean --all
   xmake f -p linux -a x86_64 -m debug
   ```

3. **Discord Game SDKライブラリが見つからない場合**:
   ```bash
   # ライブラリパスを確認
   echo $LD_LIBRARY_PATH
   ls -la third_party/discord_game_sdk/lib/x86_64/

   # 実行時にライブラリパスを指定
   LD_LIBRARY_PATH=./third_party/discord_game_sdk/lib/x86_64:$LD_LIBRARY_PATH ./build/linux/x86_64/debug/DiscordRPC-ReGen
   ```

### NixOS特有の問題

1. **システムトレイが表示されない場合**:
   ```bash
   # システムトレイサポートを確認
   echo $XDG_CURRENT_DESKTOP

   # 必要に応じてデスクトップ環境の設定を確認
   ```

2. **Qt6アプリケーションが起動しない場合**:
   ```bash
   # Qt6プラットフォームプラグインを確認
   echo $QT_QPA_PLATFORM_PLUGIN_PATH

   # デバッグ情報を有効にして実行
   QT_DEBUG_PLUGINS=1 xmake run
   ```

## ライセンス

このプロジェクトはMITライセンスの下で公開されています。

## 貢献

プルリクエストやイシューの報告を歓迎します。

## 参考資料

- [Discord Game SDK Documentation](https://discord.com/developers/docs/game-sdk/sdk-starter-guide)
- [Qt6 Documentation](https://doc.qt.io/qt-6/)
- [xmake Documentation](https://xmake.io/#/)
