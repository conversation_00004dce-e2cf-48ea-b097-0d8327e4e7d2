# Discord RPC Settings

Qt6ベースのDiscord Rich Presence設定アプリケーションです。Discord Game SDKを使用してDiscordのリッチプレゼンス機能を管理できます。

## 機能

- **Discord接続管理**: Application IDを使用してDiscordに接続
- **リッチプレゼンス設定**: 状態、詳細、画像などを設定
- **タイムスタンプ機能**: 経過時間、残り時間、カスタム時間の設定
- **設定の保存/読み込み**: 設定を永続化
- **システムトレイ統合**: バックグラウンドで動作
- **直感的なUI**: Qt6による現代的なインターフェース

## 必要な環境

- Windows 10/11
- Qt6 (widgets, core, gui)
- Discord Game SDK
- xmake ビルドシステム
- Visual Studio 2019/2022 または MinGW

## ビルド方法

1. **依存関係のインストール**:
   ```bash
   # Qt6とxmakeがインストールされていることを確認
   xmake --version
   ```

2. **プロジェクトの設定**:
   ```bash
   cd DiscordRPC-ReGen
   xmake f -p windows -a x64 -m release
   ```

3. **ビルド**:
   ```bash
   xmake
   ```

4. **実行**:
   ```bash
   xmake run
   ```

## 使用方法

### 1. Discord Application の設定

1. [Discord Developer Portal](https://discord.com/developers/applications) にアクセス
2. 新しいアプリケーションを作成
3. Application ID をコピー

### 2. アプリケーションの使用

1. **接続設定**:
   - Application ID を入力
   - "Connect" ボタンをクリック

2. **リッチプレゼンスの設定**:
   - **State**: 現在の状態（例: "In Game", "Browsing Menu"）
   - **Details**: 詳細情報（例: "Playing Solo", "Level 5"）
   - **Large Image Key**: 大きな画像のアセットキー
   - **Large Image Text**: 大きな画像のホバーテキスト
   - **Small Image Key**: 小さな画像のアセットキー
   - **Small Image Text**: 小さな画像のホバーテキスト

3. **タイムスタンプ設定**:
   - **Start Time (Elapsed)**: 開始時刻から経過時間を表示
   - **End Time (Remaining)**: 終了時刻までの残り時間を表示
   - **Custom Time**: カスタム時刻を設定

4. **操作**:
   - "Update Activity": リッチプレゼンスを更新
   - "Clear Activity": リッチプレゼンスをクリア
   - "Save Settings": 設定を保存
   - "Load Settings": 設定を読み込み

### 3. システムトレイ

- アプリケーションを閉じるとシステムトレイに最小化
- トレイアイコンをダブルクリックで表示/非表示切り替え
- 右クリックでコンテキストメニュー

## ファイル構成

```
DiscordRPC-ReGen/
├── src/
│   ├── main.cpp              # メインエントリーポイント
│   ├── settings_window.h     # メインウィンドウヘッダー
│   ├── settings_window.cpp   # メインウィンドウ実装
│   ├── discord_manager.h     # Discord SDK ラッパーヘッダー
│   └── discord_manager.cpp   # Discord SDK ラッパー実装
├── third_party/
│   └── discord_game_sdk/     # Discord Game SDK
│       ├── include/
│       └── lib/
├── xmake.lua                 # ビルド設定
└── README.md                 # このファイル
```

## トラブルシューティング

### 接続できない場合

1. Discordが起動していることを確認
2. Application IDが正しいことを確認
3. Discord Game SDKのDLLが正しく配置されていることを確認

### ビルドエラーの場合

1. Qt6が正しくインストールされていることを確認
2. xmakeの設定を確認: `xmake f --help`
3. 依存関係を再インストール: `xmake clean && xmake`

## ライセンス

このプロジェクトはMITライセンスの下で公開されています。

## 貢献

プルリクエストやイシューの報告を歓迎します。

## 参考資料

- [Discord Game SDK Documentation](https://discord.com/developers/docs/game-sdk/sdk-starter-guide)
- [Qt6 Documentation](https://doc.qt.io/qt-6/)
- [xmake Documentation](https://xmake.io/#/)
