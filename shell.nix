{ pkgs ? import <nixpkgs> {} }:

pkgs.mkShell {
  buildInputs = with pkgs; [
    # Build tools
    xmake
    cmake
    ninja
    pkg-config
    
    # Qt6 development
    qt6.full
    qt6.qtbase
    qt6.qttools
    qt6.wrapQtAppsHook
    
    # C++ development
    gcc
    gdb
    clang-tools
    
    # Additional libraries
    libGL
    libxkbcommon
    wayland
    xorg.libX11
    xorg.libXcursor
    xorg.libXrandr
    xorg.libXi
    fontconfig
    freetype
    
    # Development utilities
    git
    xxd  # for bin2c functionality (hexdump)

    # For system tray functionality
    libnotify
  ];

  shellHook = ''
    echo "Discord RPC Settings Development Environment (NixOS)"
    echo "=================================================="
    echo "Available tools:"
    echo "  - xmake: Build system"
    echo "  - Qt6: GUI framework"
    echo "  - Discord Game SDK: Located in third_party/"
    echo ""
    echo "Quick start:"
    echo "  xmake f -p linux -a x86_64 -m debug"
    echo "  xmake"
    echo "  xmake run"
    echo ""
    
    # Set up Qt6 environment
    export QT_QPA_PLATFORM_PLUGIN_PATH="${pkgs.qt6.qtbase}/lib/qt-6/plugins"
    export QML2_IMPORT_PATH="${pkgs.qt6.qtdeclarative}/lib/qt-6/qml"
    
    # Ensure Discord Game SDK library can be found
    export LD_LIBRARY_PATH="$PWD/third_party/discord_game_sdk/lib/x86_64:$LD_LIBRARY_PATH"
    
    # Make sure we can find Qt6
    export PKG_CONFIG_PATH="${pkgs.qt6.qtbase}/lib/pkgconfig:$PKG_CONFIG_PATH"
    export CMAKE_PREFIX_PATH="${pkgs.qt6.qtbase}:$CMAKE_PREFIX_PATH"
  '';

  # Qt6 specific environment variables
  QT_QPA_PLATFORM_PLUGIN_PATH = "${pkgs.qt6.qtbase}/lib/qt-6/plugins";
  QML2_IMPORT_PATH = "${pkgs.qt6.qtdeclarative}/lib/qt-6/qml";
}
