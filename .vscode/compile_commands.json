[{"directory": "/mnt/ExtremeSSD/Dev-Projects/C-and-C++/DiscordRPC-ReGen", "arguments": ["/nix/store/a0d7m3zn9p2dfa1h7ag9h2wzzr2w25sn-gcc-wrapper-14.2.1.20250322/bin/g++", "-c", "-m64", "-fvisibility=hidden", "-fvisibility-inlines-hidden", "-O3", "-std=c++17", "-I/nix/store/0nj1gjjipdyrgnm5c3nldpn4lf763h5a-qt-full-6.9.1/include", "-I/nix/store/0nj1gjjipdyrgnm5c3nldpn4lf763h5a-qt-full-6.9.1/include/QtCore", "-I/nix/store/0nj1gjjipdyrgnm5c3nldpn4lf763h5a-qt-full-6.9.1/include/QtGui", "-I/nix/store/0nj1gjjipdyrgnm5c3nldpn4lf763h5a-qt-full-6.9.1/include/QtWidgets", "-Ithird_party/discord_game_sdk/include", "-Ibuild/.gens/DiscordRPC-ReGen/linux/x86_64/release/platform/windows/idl", "-I/mnt/ExtremeSSD/Dev-Projects/C-and-C++/DiscordRPC-ReGen/build/.gens/DiscordRPC-ReGen/linux/x86_64/release/rules/qt/ui", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_CORE_LIB", "-DNDEBUG", "-o", "build/.objs/DiscordRPC-ReGen/linux/x86_64/release/src/discord_manager.cpp.o", "src/discord_manager.cpp"], "file": "src/discord_manager.cpp"}, {"directory": "/mnt/ExtremeSSD/Dev-Projects/C-and-C++/DiscordRPC-ReGen", "arguments": ["/nix/store/a0d7m3zn9p2dfa1h7ag9h2wzzr2w25sn-gcc-wrapper-14.2.1.20250322/bin/g++", "-c", "-m64", "-fvisibility=hidden", "-fvisibility-inlines-hidden", "-O3", "-std=c++17", "-I/nix/store/0nj1gjjipdyrgnm5c3nldpn4lf763h5a-qt-full-6.9.1/include", "-I/nix/store/0nj1gjjipdyrgnm5c3nldpn4lf763h5a-qt-full-6.9.1/include/QtCore", "-I/nix/store/0nj1gjjipdyrgnm5c3nldpn4lf763h5a-qt-full-6.9.1/include/QtGui", "-I/nix/store/0nj1gjjipdyrgnm5c3nldpn4lf763h5a-qt-full-6.9.1/include/QtWidgets", "-Ithird_party/discord_game_sdk/include", "-Ibuild/.gens/DiscordRPC-ReGen/linux/x86_64/release/platform/windows/idl", "-I/mnt/ExtremeSSD/Dev-Projects/C-and-C++/DiscordRPC-ReGen/build/.gens/DiscordRPC-ReGen/linux/x86_64/release/rules/qt/ui", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_CORE_LIB", "-DNDEBUG", "-o", "build/.objs/DiscordRPC-ReGen/linux/x86_64/release/src/main.cpp.o", "src/main.cpp"], "file": "src/main.cpp"}, {"directory": "/mnt/ExtremeSSD/Dev-Projects/C-and-C++/DiscordRPC-ReGen", "arguments": ["/nix/store/a0d7m3zn9p2dfa1h7ag9h2wzzr2w25sn-gcc-wrapper-14.2.1.20250322/bin/g++", "-c", "-m64", "-fvisibility=hidden", "-fvisibility-inlines-hidden", "-O3", "-std=c++17", "-I/nix/store/0nj1gjjipdyrgnm5c3nldpn4lf763h5a-qt-full-6.9.1/include", "-I/nix/store/0nj1gjjipdyrgnm5c3nldpn4lf763h5a-qt-full-6.9.1/include/QtCore", "-I/nix/store/0nj1gjjipdyrgnm5c3nldpn4lf763h5a-qt-full-6.9.1/include/QtGui", "-I/nix/store/0nj1gjjipdyrgnm5c3nldpn4lf763h5a-qt-full-6.9.1/include/QtWidgets", "-Ithird_party/discord_game_sdk/include", "-Ibuild/.gens/DiscordRPC-ReGen/linux/x86_64/release/platform/windows/idl", "-I/mnt/ExtremeSSD/Dev-Projects/C-and-C++/DiscordRPC-ReGen/build/.gens/DiscordRPC-ReGen/linux/x86_64/release/rules/qt/ui", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_CORE_LIB", "-DNDEBUG", "-o", "build/.objs/DiscordRPC-ReGen/linux/x86_64/release/src/settings_window.cpp.o", "src/settings_window.cpp"], "file": "src/settings_window.cpp"}]