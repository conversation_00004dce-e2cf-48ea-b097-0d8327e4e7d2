[{"directory": "/mnt/ExtremeSSD/Dev-Projects/C-and-C++/DiscordRPC-ReGen", "arguments": ["/nix/store/0nj1gjjipdyrgnm5c3nldpn4lf763h5a-qt-full-6.9.1/libexec/moc", "-DQT_DISABLE_DEPRECATED_BEFORE=0x060000", "-DNDEBUG", "-DQT_NO_DEBUG", "-DQT_DEPRECATED_WARNINGS", "-DQT_GUI_LIB", "-DQT_WIDGETS_LIB", "-DQT_CORE_LIB", "-Ithird_party/discord_game_sdk/include", "-Ibuild/.gens/DiscordRPC-ReGen/linux/x86_64/release/platform/windows/idl", "-I/mnt/ExtremeSSD/Dev-Projects/C-and-C++/DiscordRPC-ReGen/build/.gens/DiscordRPC-ReGen/linux/x86_64/release/rules/qt/ui", "-I/nix/store/0nj1gjjipdyrgnm5c3nldpn4lf763h5a-qt-full-6.9.1/include/QtGui", "-I/nix/store/0nj1gjjipdyrgnm5c3nldpn4lf763h5a-qt-full-6.9.1/include/QtGui/6.9.1", "-I/nix/store/0nj1gjjipdyrgnm5c3nldpn4lf763h5a-qt-full-6.9.1/include/QtGui/6.9.1/QtGui", "-I/nix/store/0nj1gjjipdyrgnm5c3nldpn4lf763h5a-qt-full-6.9.1/include/QtWidgets", "-I/nix/store/0nj1gjjipdyrgnm5c3nldpn4lf763h5a-qt-full-6.9.1/include/QtWidgets/6.9.1", "-I/nix/store/0nj1gjjipdyrgnm5c3nldpn4lf763h5a-qt-full-6.9.1/include/QtWidgets/6.9.1/QtWidgets", "-I/nix/store/0nj1gjjipdyrgnm5c3nldpn4lf763h5a-qt-full-6.9.1/include/QtCore", "-I/nix/store/0nj1gjjipdyrgnm5c3nldpn4lf763h5a-qt-full-6.9.1/include/QtCore/6.9.1", "-I/nix/store/0nj1gjjipdyrgnm5c3nldpn4lf763h5a-qt-full-6.9.1/include/QtCore/6.9.1/QtCore", "-I/nix/store/0nj1gjjipdyrgnm5c3nldpn4lf763h5a-qt-full-6.9.1/include", "-I/nix/store/0nj1gjjipdyrgnm5c3nldpn4lf763h5a-qt-full-6.9.1/mkspecs/linux-g++", "src/discord_manager.h", "-o", "build/.gens/DiscordRPC-ReGen/linux/x86_64/release/src/moc_discord_manager.cpp"], "file": "build/.gens/DiscordRPC-ReGen/linux/x86_64/release/src/moc_discord_manager.cpp"}, {"directory": "/mnt/ExtremeSSD/Dev-Projects/C-and-C++/DiscordRPC-ReGen", "arguments": ["/nix/store/a0d7m3zn9p2dfa1h7ag9h2wzzr2w25sn-gcc-wrapper-14.2.1.20250322/bin/g++", "-c", "-m64", "-fvisibility=hidden", "-fvisibility-inlines-hidden", "-O3", "-std=c++17", "-Ithird_party/discord_game_sdk/include", "-Ibuild/.gens/DiscordRPC-ReGen/linux/x86_64/release/platform/windows/idl", "-I/mnt/ExtremeSSD/Dev-Projects/C-and-C++/DiscordRPC-ReGen/build/.gens/DiscordRPC-ReGen/linux/x86_64/release/rules/qt/ui", "-DQT_DISABLE_DEPRECATED_BEFORE=0x060000", "-DQT_NO_DEBUG", "-DQT_DEPRECATED_WARNINGS", "-DQT_GUI_LIB", "-DQT_WIDGETS_LIB", "-DQT_CORE_LIB", "-isystem", "/nix/store/0nj1gjjipdyrgnm5c3nldpn4lf763h5a-qt-full-6.9.1/include/QtGui", "-isystem", "/nix/store/0nj1gjjipdyrgnm5c3nldpn4lf763h5a-qt-full-6.9.1/include/QtGui/6.9.1", "-isystem", "/nix/store/0nj1gjjipdyrgnm5c3nldpn4lf763h5a-qt-full-6.9.1/include/QtGui/6.9.1/QtGui", "-isystem", "/nix/store/0nj1gjjipdyrgnm5c3nldpn4lf763h5a-qt-full-6.9.1/include/QtWidgets", "-isystem", "/nix/store/0nj1gjjipdyrgnm5c3nldpn4lf763h5a-qt-full-6.9.1/include/QtWidgets/6.9.1", "-isystem", "/nix/store/0nj1gjjipdyrgnm5c3nldpn4lf763h5a-qt-full-6.9.1/include/QtWidgets/6.9.1/QtWidgets", "-isystem", "/nix/store/0nj1gjjipdyrgnm5c3nldpn4lf763h5a-qt-full-6.9.1/include/QtCore", "-isystem", "/nix/store/0nj1gjjipdyrgnm5c3nldpn4lf763h5a-qt-full-6.9.1/include/QtCore/6.9.1", "-isystem", "/nix/store/0nj1gjjipdyrgnm5c3nldpn4lf763h5a-qt-full-6.9.1/include/QtCore/6.9.1/QtCore", "-isystem", "/nix/store/0nj1gjjipdyrgnm5c3nldpn4lf763h5a-qt-full-6.9.1/include", "-isystem", "/nix/store/0nj1gjjipdyrgnm5c3nldpn4lf763h5a-qt-full-6.9.1/mkspecs/linux-g++", "-DNDEBUG", "-fPIC", "-o", "build/.objs/DiscordRPC-ReGen/linux/x86_64/release/gens/src/moc_discord_manager.cpp.o", "build/.gens/DiscordRPC-ReGen/linux/x86_64/release/src/moc_discord_manager.cpp"], "file": "build/.gens/DiscordRPC-ReGen/linux/x86_64/release/src/moc_discord_manager.cpp"}, {"directory": "/mnt/ExtremeSSD/Dev-Projects/C-and-C++/DiscordRPC-ReGen", "arguments": ["/nix/store/0nj1gjjipdyrgnm5c3nldpn4lf763h5a-qt-full-6.9.1/libexec/moc", "-DQT_DISABLE_DEPRECATED_BEFORE=0x060000", "-DNDEBUG", "-DQT_NO_DEBUG", "-DQT_DEPRECATED_WARNINGS", "-DQT_GUI_LIB", "-DQT_WIDGETS_LIB", "-DQT_CORE_LIB", "-Ithird_party/discord_game_sdk/include", "-Ibuild/.gens/DiscordRPC-ReGen/linux/x86_64/release/platform/windows/idl", "-I/mnt/ExtremeSSD/Dev-Projects/C-and-C++/DiscordRPC-ReGen/build/.gens/DiscordRPC-ReGen/linux/x86_64/release/rules/qt/ui", "-I/nix/store/0nj1gjjipdyrgnm5c3nldpn4lf763h5a-qt-full-6.9.1/include/QtGui", "-I/nix/store/0nj1gjjipdyrgnm5c3nldpn4lf763h5a-qt-full-6.9.1/include/QtGui/6.9.1", "-I/nix/store/0nj1gjjipdyrgnm5c3nldpn4lf763h5a-qt-full-6.9.1/include/QtGui/6.9.1/QtGui", "-I/nix/store/0nj1gjjipdyrgnm5c3nldpn4lf763h5a-qt-full-6.9.1/include/QtWidgets", "-I/nix/store/0nj1gjjipdyrgnm5c3nldpn4lf763h5a-qt-full-6.9.1/include/QtWidgets/6.9.1", "-I/nix/store/0nj1gjjipdyrgnm5c3nldpn4lf763h5a-qt-full-6.9.1/include/QtWidgets/6.9.1/QtWidgets", "-I/nix/store/0nj1gjjipdyrgnm5c3nldpn4lf763h5a-qt-full-6.9.1/include/QtCore", "-I/nix/store/0nj1gjjipdyrgnm5c3nldpn4lf763h5a-qt-full-6.9.1/include/QtCore/6.9.1", "-I/nix/store/0nj1gjjipdyrgnm5c3nldpn4lf763h5a-qt-full-6.9.1/include/QtCore/6.9.1/QtCore", "-I/nix/store/0nj1gjjipdyrgnm5c3nldpn4lf763h5a-qt-full-6.9.1/include", "-I/nix/store/0nj1gjjipdyrgnm5c3nldpn4lf763h5a-qt-full-6.9.1/mkspecs/linux-g++", "src/settings_window.h", "-o", "build/.gens/DiscordRPC-ReGen/linux/x86_64/release/src/moc_settings_window.cpp"], "file": "build/.gens/DiscordRPC-ReGen/linux/x86_64/release/src/moc_settings_window.cpp"}, {"directory": "/mnt/ExtremeSSD/Dev-Projects/C-and-C++/DiscordRPC-ReGen", "arguments": ["/nix/store/a0d7m3zn9p2dfa1h7ag9h2wzzr2w25sn-gcc-wrapper-14.2.1.20250322/bin/g++", "-c", "-m64", "-fvisibility=hidden", "-fvisibility-inlines-hidden", "-O3", "-std=c++17", "-Ithird_party/discord_game_sdk/include", "-Ibuild/.gens/DiscordRPC-ReGen/linux/x86_64/release/platform/windows/idl", "-I/mnt/ExtremeSSD/Dev-Projects/C-and-C++/DiscordRPC-ReGen/build/.gens/DiscordRPC-ReGen/linux/x86_64/release/rules/qt/ui", "-DQT_DISABLE_DEPRECATED_BEFORE=0x060000", "-DQT_NO_DEBUG", "-DQT_DEPRECATED_WARNINGS", "-DQT_GUI_LIB", "-DQT_WIDGETS_LIB", "-DQT_CORE_LIB", "-isystem", "/nix/store/0nj1gjjipdyrgnm5c3nldpn4lf763h5a-qt-full-6.9.1/include/QtGui", "-isystem", "/nix/store/0nj1gjjipdyrgnm5c3nldpn4lf763h5a-qt-full-6.9.1/include/QtGui/6.9.1", "-isystem", "/nix/store/0nj1gjjipdyrgnm5c3nldpn4lf763h5a-qt-full-6.9.1/include/QtGui/6.9.1/QtGui", "-isystem", "/nix/store/0nj1gjjipdyrgnm5c3nldpn4lf763h5a-qt-full-6.9.1/include/QtWidgets", "-isystem", "/nix/store/0nj1gjjipdyrgnm5c3nldpn4lf763h5a-qt-full-6.9.1/include/QtWidgets/6.9.1", "-isystem", "/nix/store/0nj1gjjipdyrgnm5c3nldpn4lf763h5a-qt-full-6.9.1/include/QtWidgets/6.9.1/QtWidgets", "-isystem", "/nix/store/0nj1gjjipdyrgnm5c3nldpn4lf763h5a-qt-full-6.9.1/include/QtCore", "-isystem", "/nix/store/0nj1gjjipdyrgnm5c3nldpn4lf763h5a-qt-full-6.9.1/include/QtCore/6.9.1", "-isystem", "/nix/store/0nj1gjjipdyrgnm5c3nldpn4lf763h5a-qt-full-6.9.1/include/QtCore/6.9.1/QtCore", "-isystem", "/nix/store/0nj1gjjipdyrgnm5c3nldpn4lf763h5a-qt-full-6.9.1/include", "-isystem", "/nix/store/0nj1gjjipdyrgnm5c3nldpn4lf763h5a-qt-full-6.9.1/mkspecs/linux-g++", "-DNDEBUG", "-fPIC", "-o", "build/.objs/DiscordRPC-ReGen/linux/x86_64/release/gens/src/moc_settings_window.cpp.o", "build/.gens/DiscordRPC-ReGen/linux/x86_64/release/src/moc_settings_window.cpp"], "file": "build/.gens/DiscordRPC-ReGen/linux/x86_64/release/src/moc_settings_window.cpp"}, {"directory": "/mnt/ExtremeSSD/Dev-Projects/C-and-C++/DiscordRPC-ReGen", "arguments": ["/nix/store/a0d7m3zn9p2dfa1h7ag9h2wzzr2w25sn-gcc-wrapper-14.2.1.20250322/bin/g++", "-c", "-m64", "-fvisibility=hidden", "-fvisibility-inlines-hidden", "-O3", "-std=c++17", "-Ithird_party/discord_game_sdk/include", "-Ibuild/.gens/DiscordRPC-ReGen/linux/x86_64/release/platform/windows/idl", "-I/mnt/ExtremeSSD/Dev-Projects/C-and-C++/DiscordRPC-ReGen/build/.gens/DiscordRPC-ReGen/linux/x86_64/release/rules/qt/ui", "-DQT_DISABLE_DEPRECATED_BEFORE=0x060000", "-DQT_NO_DEBUG", "-DQT_DEPRECATED_WARNINGS", "-DQT_GUI_LIB", "-DQT_WIDGETS_LIB", "-DQT_CORE_LIB", "-isystem", "/nix/store/0nj1gjjipdyrgnm5c3nldpn4lf763h5a-qt-full-6.9.1/include/QtGui", "-isystem", "/nix/store/0nj1gjjipdyrgnm5c3nldpn4lf763h5a-qt-full-6.9.1/include/QtGui/6.9.1", "-isystem", "/nix/store/0nj1gjjipdyrgnm5c3nldpn4lf763h5a-qt-full-6.9.1/include/QtGui/6.9.1/QtGui", "-isystem", "/nix/store/0nj1gjjipdyrgnm5c3nldpn4lf763h5a-qt-full-6.9.1/include/QtWidgets", "-isystem", "/nix/store/0nj1gjjipdyrgnm5c3nldpn4lf763h5a-qt-full-6.9.1/include/QtWidgets/6.9.1", "-isystem", "/nix/store/0nj1gjjipdyrgnm5c3nldpn4lf763h5a-qt-full-6.9.1/include/QtWidgets/6.9.1/QtWidgets", "-isystem", "/nix/store/0nj1gjjipdyrgnm5c3nldpn4lf763h5a-qt-full-6.9.1/include/QtCore", "-isystem", "/nix/store/0nj1gjjipdyrgnm5c3nldpn4lf763h5a-qt-full-6.9.1/include/QtCore/6.9.1", "-isystem", "/nix/store/0nj1gjjipdyrgnm5c3nldpn4lf763h5a-qt-full-6.9.1/include/QtCore/6.9.1/QtCore", "-isystem", "/nix/store/0nj1gjjipdyrgnm5c3nldpn4lf763h5a-qt-full-6.9.1/include", "-isystem", "/nix/store/0nj1gjjipdyrgnm5c3nldpn4lf763h5a-qt-full-6.9.1/mkspecs/linux-g++", "-DNDEBUG", "-fPIC", "-o", "build/.objs/DiscordRPC-ReGen/linux/x86_64/release/src/discord_manager.cpp.o", "src/discord_manager.cpp"], "file": "src/discord_manager.cpp"}, {"directory": "/mnt/ExtremeSSD/Dev-Projects/C-and-C++/DiscordRPC-ReGen", "arguments": ["/nix/store/a0d7m3zn9p2dfa1h7ag9h2wzzr2w25sn-gcc-wrapper-14.2.1.20250322/bin/g++", "-c", "-m64", "-fvisibility=hidden", "-fvisibility-inlines-hidden", "-O3", "-std=c++17", "-Ithird_party/discord_game_sdk/include", "-Ibuild/.gens/DiscordRPC-ReGen/linux/x86_64/release/platform/windows/idl", "-I/mnt/ExtremeSSD/Dev-Projects/C-and-C++/DiscordRPC-ReGen/build/.gens/DiscordRPC-ReGen/linux/x86_64/release/rules/qt/ui", "-DQT_DISABLE_DEPRECATED_BEFORE=0x060000", "-DQT_NO_DEBUG", "-DQT_DEPRECATED_WARNINGS", "-DQT_GUI_LIB", "-DQT_WIDGETS_LIB", "-DQT_CORE_LIB", "-isystem", "/nix/store/0nj1gjjipdyrgnm5c3nldpn4lf763h5a-qt-full-6.9.1/include/QtGui", "-isystem", "/nix/store/0nj1gjjipdyrgnm5c3nldpn4lf763h5a-qt-full-6.9.1/include/QtGui/6.9.1", "-isystem", "/nix/store/0nj1gjjipdyrgnm5c3nldpn4lf763h5a-qt-full-6.9.1/include/QtGui/6.9.1/QtGui", "-isystem", "/nix/store/0nj1gjjipdyrgnm5c3nldpn4lf763h5a-qt-full-6.9.1/include/QtWidgets", "-isystem", "/nix/store/0nj1gjjipdyrgnm5c3nldpn4lf763h5a-qt-full-6.9.1/include/QtWidgets/6.9.1", "-isystem", "/nix/store/0nj1gjjipdyrgnm5c3nldpn4lf763h5a-qt-full-6.9.1/include/QtWidgets/6.9.1/QtWidgets", "-isystem", "/nix/store/0nj1gjjipdyrgnm5c3nldpn4lf763h5a-qt-full-6.9.1/include/QtCore", "-isystem", "/nix/store/0nj1gjjipdyrgnm5c3nldpn4lf763h5a-qt-full-6.9.1/include/QtCore/6.9.1", "-isystem", "/nix/store/0nj1gjjipdyrgnm5c3nldpn4lf763h5a-qt-full-6.9.1/include/QtCore/6.9.1/QtCore", "-isystem", "/nix/store/0nj1gjjipdyrgnm5c3nldpn4lf763h5a-qt-full-6.9.1/include", "-isystem", "/nix/store/0nj1gjjipdyrgnm5c3nldpn4lf763h5a-qt-full-6.9.1/mkspecs/linux-g++", "-DNDEBUG", "-fPIC", "-o", "build/.objs/DiscordRPC-ReGen/linux/x86_64/release/src/main.cpp.o", "src/main.cpp"], "file": "src/main.cpp"}, {"directory": "/mnt/ExtremeSSD/Dev-Projects/C-and-C++/DiscordRPC-ReGen", "arguments": ["/nix/store/a0d7m3zn9p2dfa1h7ag9h2wzzr2w25sn-gcc-wrapper-14.2.1.20250322/bin/g++", "-c", "-m64", "-fvisibility=hidden", "-fvisibility-inlines-hidden", "-O3", "-std=c++17", "-Ithird_party/discord_game_sdk/include", "-Ibuild/.gens/DiscordRPC-ReGen/linux/x86_64/release/platform/windows/idl", "-I/mnt/ExtremeSSD/Dev-Projects/C-and-C++/DiscordRPC-ReGen/build/.gens/DiscordRPC-ReGen/linux/x86_64/release/rules/qt/ui", "-DQT_DISABLE_DEPRECATED_BEFORE=0x060000", "-DQT_NO_DEBUG", "-DQT_DEPRECATED_WARNINGS", "-DQT_GUI_LIB", "-DQT_WIDGETS_LIB", "-DQT_CORE_LIB", "-isystem", "/nix/store/0nj1gjjipdyrgnm5c3nldpn4lf763h5a-qt-full-6.9.1/include/QtGui", "-isystem", "/nix/store/0nj1gjjipdyrgnm5c3nldpn4lf763h5a-qt-full-6.9.1/include/QtGui/6.9.1", "-isystem", "/nix/store/0nj1gjjipdyrgnm5c3nldpn4lf763h5a-qt-full-6.9.1/include/QtGui/6.9.1/QtGui", "-isystem", "/nix/store/0nj1gjjipdyrgnm5c3nldpn4lf763h5a-qt-full-6.9.1/include/QtWidgets", "-isystem", "/nix/store/0nj1gjjipdyrgnm5c3nldpn4lf763h5a-qt-full-6.9.1/include/QtWidgets/6.9.1", "-isystem", "/nix/store/0nj1gjjipdyrgnm5c3nldpn4lf763h5a-qt-full-6.9.1/include/QtWidgets/6.9.1/QtWidgets", "-isystem", "/nix/store/0nj1gjjipdyrgnm5c3nldpn4lf763h5a-qt-full-6.9.1/include/QtCore", "-isystem", "/nix/store/0nj1gjjipdyrgnm5c3nldpn4lf763h5a-qt-full-6.9.1/include/QtCore/6.9.1", "-isystem", "/nix/store/0nj1gjjipdyrgnm5c3nldpn4lf763h5a-qt-full-6.9.1/include/QtCore/6.9.1/QtCore", "-isystem", "/nix/store/0nj1gjjipdyrgnm5c3nldpn4lf763h5a-qt-full-6.9.1/include", "-isystem", "/nix/store/0nj1gjjipdyrgnm5c3nldpn4lf763h5a-qt-full-6.9.1/mkspecs/linux-g++", "-DNDEBUG", "-fPIC", "-o", "build/.objs/DiscordRPC-ReGen/linux/x86_64/release/src/settings_window.cpp.o", "src/settings_window.cpp"], "file": "src/settings_window.cpp"}]