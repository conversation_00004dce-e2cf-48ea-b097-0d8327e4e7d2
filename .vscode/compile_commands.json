[{"directory": "/mnt/ExtremeSSD/Dev-Projects/C-and-C++/DiscordRPC-ReGen", "arguments": ["/run/current-system/sw/bin/gcc", "-c", "-m64", "-target", "x86_64-linux-gnu", "-std=c++17", "-Ithird_party/discord_game_sdk/include", "-Ibuild/.gens/DiscordRPC-ReGen/linux/x86_64/platform/windows/idl", "-I/mnt/ExtremeSSD/Dev-Projects/C-and-C++/DiscordRPC-ReGen/build/.gens/DiscordRPC-ReGen/linux/x86_64/rules/qt/ui", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_CORE_LIB", "-framework", "QtWidgets", "-framework", "QtCore", "-framework", "QtGui", "-o", "build/.objs/DiscordRPC-ReGen/linux/x86_64/src/discord_manager.cpp.o", "src/discord_manager.cpp"], "file": "src/discord_manager.cpp"}, {"directory": "/mnt/ExtremeSSD/Dev-Projects/C-and-C++/DiscordRPC-ReGen", "arguments": ["/run/current-system/sw/bin/gcc", "-c", "-m64", "-target", "x86_64-linux-gnu", "-std=c++17", "-Ithird_party/discord_game_sdk/include", "-Ibuild/.gens/DiscordRPC-ReGen/linux/x86_64/platform/windows/idl", "-I/mnt/ExtremeSSD/Dev-Projects/C-and-C++/DiscordRPC-ReGen/build/.gens/DiscordRPC-ReGen/linux/x86_64/rules/qt/ui", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_CORE_LIB", "-framework", "QtWidgets", "-framework", "QtCore", "-framework", "QtGui", "-o", "build/.objs/DiscordRPC-ReGen/linux/x86_64/src/main.cpp.o", "src/main.cpp"], "file": "src/main.cpp"}, {"directory": "/mnt/ExtremeSSD/Dev-Projects/C-and-C++/DiscordRPC-ReGen", "arguments": ["/run/current-system/sw/bin/gcc", "-c", "-m64", "-target", "x86_64-linux-gnu", "-std=c++17", "-Ithird_party/discord_game_sdk/include", "-Ibuild/.gens/DiscordRPC-ReGen/linux/x86_64/platform/windows/idl", "-I/mnt/ExtremeSSD/Dev-Projects/C-and-C++/DiscordRPC-ReGen/build/.gens/DiscordRPC-ReGen/linux/x86_64/rules/qt/ui", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_CORE_LIB", "-framework", "QtWidgets", "-framework", "QtCore", "-framework", "QtGui", "-o", "build/.objs/DiscordRPC-ReGen/linux/x86_64/src/settings_window.cpp.o", "src/settings_window.cpp"], "file": "src/settings_window.cpp"}]