-- xmake version
set_xmakever("2.7.1")

-- project
set_project("DiscordRPC-ReGen")

-- set project version
set_version("1.0.0")

-- set language: c++17
set_languages("c++17")

-- add rules
add_rules("mode.debug", "mode.release")

-- target
target("DiscordRPC-ReGen")
    set_kind("binary")

    -- enable Qt application with widgets
    add_rules("qt.widgetapp")

    -- add source files
    add_files("src/*.cpp")

    -- add header files (this is important for MOC)
    add_files("src/*.h")

    -- set target directory
    set_targetdir("build")

    -- add defines for Qt6
    add_defines("QT_DISABLE_DEPRECATED_BEFORE=0x060000")

    -- ensure Qt6 modules are linked
    add_frameworks("QtCore", "QtGui", "QtWidgets")

    -- platform specific settings
    if is_plat("linux") then
        add_syslinks("pthread", "dl")
    end

    -- Add Discord Game SDK
    add_includedirs("third_party/discord_game_sdk/include")

    if is_plat("linux") then
        -- Copy shared library to build directory and link it
        before_build(function (target)
            os.cp("third_party/discord_game_sdk/lib/x86_64/discord_game_sdk.so", "build/")
        end)

        -- Link the copied library
        add_linkdirs("build")
        add_links("discord_game_sdk")

        -- Set rpath to find the library at runtime
        add_ldflags("-Wl,-rpath,.")
    elseif is_plat("windows") then
        add_linkdirs("third_party/discord_game_sdk/lib/x86_64")
        add_links("discord_game_sdk")
        after_build(function (target)
            os.cp("third_party/discord_game_sdk/lib/x86_64/discord_game_sdk.dll", target:targetdir())
        end)
    end

    -- debug mode settings
    if is_mode("debug") then
        set_symbols("debug")
        set_optimize("none")
        add_defines("DEBUG")
    end

    -- release mode settings
    if is_mode("release") then
        set_symbols("hidden")
        set_optimize("fastest")
        set_strip("all")
        add_defines("NDEBUG")
    end

--
-- If you want to known more usage about xmake, please see https://xmake.io
--
-- ## FAQ
--
-- You can enter the project directory firstly before building project.
--
--   $ cd projectdir
--
-- 1. How to build project?
--
--   $ xmake
--
-- 2. How to configure project?
--
--   $ xmake f -p [macosx|linux|iphoneos ..] -a [x86_64|i386|arm64 ..] -m [debug|release]
--
-- 3. Where is the build output directory?
--
--   The default output directory is `./build` and you can configure the output directory.
--
--   $ xmake f -o outputdir
--   $ xmake
--
-- 4. How to run and debug target after building project?
--
--   $ xmake run [targetname]
--   $ xmake run -d [targetname]
--
-- 5. How to install target to the system directory or other output directory?
--
--   $ xmake install
--   $ xmake install -o installdir
--
-- 6. Add some frequently-used compilation flags in xmake.lua
--
-- @code
--    -- add debug and release modes
--    add_rules("mode.debug", "mode.release")
--
--    -- add macro definition
--    add_defines("NDEBUG", "_GNU_SOURCE=1")
--
--    -- set warning all as error
--    set_warnings("all", "error")
--
--    -- set language: c99, c++11
--    set_languages("c99", "c++11")
--
--    -- set optimization: none, faster, fastest, smallest
--    set_optimize("fastest")
--
--    -- add include search directories
--    add_includedirs("/usr/include", "/usr/local/include")
--
--    -- add link libraries and search directories
--    add_links("tbox")
--    add_linkdirs("/usr/local/lib", "/usr/lib")
--
--    -- add system link libraries
--    add_syslinks("z", "pthread")
--
--    -- add compilation and link flags
--    add_cxflags("-stdnolib", "-fno-strict-aliasing")
--    add_ldflags("-L/usr/local/lib", "-lpthread", {force = true})
--
-- @endcode
--

