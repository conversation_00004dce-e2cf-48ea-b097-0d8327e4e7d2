#!/bin/bash

# bin2c.sh - Convert binary file to C header using xxd
# Usage: bin2c.sh input_file output_file variable_name

if [ $# -ne 3 ]; then
    echo "Usage: $0 <input_file> <output_file> <variable_name>"
    exit 1
fi

INPUT_FILE="$1"
OUTPUT_FILE="$2"
VARIABLE_NAME="$3"

if [ ! -f "$INPUT_FILE" ]; then
    echo "Error: Input file '$INPUT_FILE' not found"
    exit 1
fi

# Create output directory if it doesn't exist
mkdir -p "$(dirname "$OUTPUT_FILE")"

# Generate header file
cat > "$OUTPUT_FILE" << EOF
// Auto-generated file - do not edit manually
// Generated from: $INPUT_FILE

#ifndef ${VARIABLE_NAME^^}_H
#define ${VARIABLE_NAME^^}_H

#include <cstddef>

extern const unsigned char ${VARIABLE_NAME}_data[];
extern const size_t ${VARIABLE_NAME}_size;

#endif // ${VARIABLE_NAME^^}_H
EOF

# Generate source file
SOURCE_FILE="${OUTPUT_FILE%.h}.cpp"
cat > "$SOURCE_FILE" << EOF
// Auto-generated file - do not edit manually
// Generated from: $INPUT_FILE

#include "$(basename "$OUTPUT_FILE")"

EOF

# Use xxd to generate the data array
xxd -i "$INPUT_FILE" | sed "s/unsigned char .*/const unsigned char ${VARIABLE_NAME}_data[] = {/" >> "$SOURCE_FILE"
echo "" >> "$SOURCE_FILE"
echo "const size_t ${VARIABLE_NAME}_size = sizeof(${VARIABLE_NAME}_data);" >> "$SOURCE_FILE"

echo "Generated: $OUTPUT_FILE and $SOURCE_FILE"
