#ifndef DISCORD_MANAGER_H
#define DISCORD_MANAGER_H

#include <QObject>
#include <QString>
#include <QTimer>
#include <memory>
#include "discord.h"

class DiscordManager : public QObject
{
    Q_OBJECT

public:
    struct ActivityData {
        QString state;
        QString details;
        QString largeImageKey;
        QString largeImageText;
        QString smallImageKey;
        QString smallImageText;
        int64_t startTimestamp = 0;
        int64_t endTimestamp = 0;
    };

    explicit DiscordManager(QObject *parent = nullptr);
    ~DiscordManager();

    bool initialize(int64_t applicationId);
    void shutdown();
    bool isConnected() const;
    
    void setActivity(const ActivityData& activity);
    void clearActivity();
    
    QString getUsername() const;
    QString getDiscriminator() const;
    QString getUserId() const;

signals:
    void connected();
    void disconnected();
    void activityUpdated();
    void errorOccurred(const QString& error);

private slots:
    void runCallbacks();

private:
    std::unique_ptr<discord::Core> m_core;
    QTimer* m_callbackTimer;
    bool m_isConnected;

    void updateCurrentUser();
};

#endif // DISCORD_MANAGER_H
