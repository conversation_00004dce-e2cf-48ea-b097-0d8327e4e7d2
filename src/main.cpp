#include <QApplication>
#include <QMessageBox>
#include <QSystemTrayIcon>
#include <QDir>
#include <QStandardPaths>
#include "settings_window.h"

int main(int argc, char** argv) {
    QApplication app(argc, argv);

    // Set application properties
    app.setApplicationName("DiscordRPC-ReGen");
    app.setApplicationVersion("1.0");
    app.setOrganizationName("DiscordRPC-ReGen");
    app.setOrganizationDomain("discord-rpc-regen.local");

    // Check if system tray is available
    if (!QSystemTrayIcon::isSystemTrayAvailable()) {
        QMessageBox::critical(nullptr, "System Tray",
                             "System tray is not available on this system.");
        return 1;
    }

    // Prevent multiple instances
    app.setQuitOnLastWindowClosed(false);

    // Create and show the main window
    SettingsWindow window;
    window.show();

    return app.exec();
}
