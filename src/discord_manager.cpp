#include "discord_manager.h"
#include <QDebug>
#include <cstring>

DiscordManager::DiscordManager(QObject *parent)
    : QObject(parent)
    , m_core(nullptr)
    , m_callbackTimer(new QTimer(this))
    , m_isConnected(false)
{
    connect(m_callbackTimer, &QTimer::timeout, this, &DiscordManager::runCallbacks);
}

DiscordManager::~DiscordManager()
{
    shutdown();
}

bool DiscordManager::initialize(int64_t applicationId)
{
    if (m_core) {
        qWarning() << "Discord Manager already initialized";
        return true;
    }

    discord::Core* core{};
    auto result = discord::Core::Create(applicationId, DiscordCreateFlags_Default, &core);

    if (result != discord::Result::Ok) {
        qWarning() << "Failed to create Discord instance:" << static_cast<int>(result);
        return false;
    }

    m_core.reset(core);

    // Start callback timer
    m_callbackTimer->start(16); // ~60 FPS

    m_isConnected = true;
    emit connected();

    qDebug() << "Discord Manager initialized successfully";
    return true;

}

void DiscordManager::shutdown()
{
    if (m_callbackTimer) {
        m_callbackTimer->stop();
    }

    if (m_core) {
        m_core.reset();
    }

    if (m_isConnected) {
        m_isConnected = false;
        emit disconnected();
    }
}

bool DiscordManager::isConnected() const
{
    return m_isConnected;
}

void DiscordManager::setActivity(const ActivityData& activity)
{
    if (!m_core) {
        qWarning() << "Discord core not available";
        return;
    }

    discord::Activity discordActivity{};

    // Set activity data
    if (!activity.state.isEmpty()) {
        discordActivity.SetState(activity.state.toUtf8().constData());
    }

    if (!activity.details.isEmpty()) {
        discordActivity.SetDetails(activity.details.toUtf8().constData());
    }

    if (!activity.largeImageKey.isEmpty()) {
        discordActivity.GetAssets().SetLargeImage(activity.largeImageKey.toUtf8().constData());
    }

    if (!activity.largeImageText.isEmpty()) {
        discordActivity.GetAssets().SetLargeText(activity.largeImageText.toUtf8().constData());
    }

    if (!activity.smallImageKey.isEmpty()) {
        discordActivity.GetAssets().SetSmallImage(activity.smallImageKey.toUtf8().constData());
    }

    if (!activity.smallImageText.isEmpty()) {
        discordActivity.GetAssets().SetSmallText(activity.smallImageText.toUtf8().constData());
    }

    if (activity.startTimestamp > 0) {
        discordActivity.GetTimestamps().SetStart(activity.startTimestamp);
    }

    if (activity.endTimestamp > 0) {
        discordActivity.GetTimestamps().SetEnd(activity.endTimestamp);
    }

    m_core->ActivityManager().UpdateActivity(discordActivity, [this](discord::Result result) {
        if (result == discord::Result::Ok) {
            emit activityUpdated();
        } else {
            qWarning() << "Failed to update activity:" << static_cast<int>(result);
        }
    });
}

void DiscordManager::clearActivity()
{
    if (!m_core) {
        qWarning() << "Discord core not available";
        return;
    }

    m_core->ActivityManager().ClearActivity([this](discord::Result result) {
        if (result == discord::Result::Ok) {
            emit activityUpdated();
        } else {
            qWarning() << "Failed to clear activity:" << static_cast<int>(result);
        }
    });
}

QString DiscordManager::getUsername() const
{
    if (!m_core) {
        return QString();
    }

    discord::User currentUser;
    auto result = m_core->UserManager().GetCurrentUser(&currentUser);

    if (result == discord::Result::Ok) {
        return QString::fromUtf8(currentUser.GetUsername());
    }

    return QString();
}

QString DiscordManager::getDiscriminator() const
{
    if (!m_core) {
        return QString();
    }

    discord::User currentUser;
    auto result = m_core->UserManager().GetCurrentUser(&currentUser);

    if (result == discord::Result::Ok) {
        return QString::fromUtf8(currentUser.GetDiscriminator());
    }

    return QString();
}

QString DiscordManager::getUserId() const
{
    if (!m_core) {
        return QString();
    }

    discord::User currentUser;
    auto result = m_core->UserManager().GetCurrentUser(&currentUser);

    if (result == discord::Result::Ok) {
        return QString::number(currentUser.GetId());
    }

    return QString();
}

void DiscordManager::runCallbacks()
{
    if (m_core) {
        auto result = m_core->RunCallbacks();
        if (result != discord::Result::Ok) {
            qWarning() << "Discord callback error:" << static_cast<int>(result);
        }
    }
}

void DiscordManager::updateCurrentUser()
{
    // Update user information
    emit connected();
}


