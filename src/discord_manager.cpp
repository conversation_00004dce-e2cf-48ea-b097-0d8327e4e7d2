#include "discord_manager.h"
#include <QDebug>
#include <cstring>

DiscordManager::DiscordManager(QObject *parent)
    : QObject(parent)
    , m_core(nullptr)
    , m_activityManager(nullptr)
    , m_userManager(nullptr)
    , m_callbackTimer(new QTimer(this))
    , m_isConnected(false)
{
    connect(m_callbackTimer, &QTimer::timeout, this, &DiscordManager::runCallbacks);
}

DiscordManager::~DiscordManager()
{
    shutdown();
}

bool DiscordManager::initialize(int64_t applicationId)
{
    Q_UNUSED(applicationId)

    // Temporarily disable Discord SDK for testing
    qDebug() << "Discord Manager initialize called (disabled for testing)";

    // Simulate successful connection for UI testing
    m_isConnected = true;
    emit connected();

    return true;

    /*
    if (m_core) {
        qWarning() << "Discord Manager already initialized";
        return true;
    }

    struct DiscordCreateParams params;
    DiscordCreateParamsSetDefault(&params);
    params.client_id = applicationId;
    params.flags = DiscordCreateFlags_Default;

    // Set up event handlers
    params.user_events = (struct IDiscordUserEvents*)malloc(sizeof(struct IDiscordUserEvents));
    memset(params.user_events, 0, sizeof(struct IDiscordUserEvents));
    params.user_events->on_current_user_update = onCurrentUserUpdate;

    enum EDiscordResult result = DiscordCreate(DISCORD_VERSION, &params, &m_core);

    if (result != DiscordResult_Ok) {
        qWarning() << "Failed to create Discord instance:" << result;
        if (params.user_events) {
            free(params.user_events);
        }
        return false;
    }

    m_activityManager = m_core->get_activity_manager(m_core);
    m_userManager = m_core->get_user_manager(m_core);

    // Start callback timer
    m_callbackTimer->start(16); // ~60 FPS

    m_isConnected = true;
    emit connected();

    qDebug() << "Discord Manager initialized successfully";
    return true;
    */
}

void DiscordManager::shutdown()
{
    if (m_callbackTimer) {
        m_callbackTimer->stop();
    }
    
    if (m_core) {
        m_core->destroy(m_core);
        m_core = nullptr;
        m_activityManager = nullptr;
        m_userManager = nullptr;
    }
    
    if (m_isConnected) {
        m_isConnected = false;
        emit disconnected();
    }
}

bool DiscordManager::isConnected() const
{
    return m_isConnected;
}

void DiscordManager::setActivity(const ActivityData& activity)
{
    Q_UNUSED(activity)

    // Temporarily disabled for testing
    qDebug() << "setActivity called (disabled for testing)";
    emit activityUpdated();

    /*
    if (!m_activityManager) {
        qWarning() << "Activity manager not available";
        return;
    }

    struct DiscordActivity discordActivity;
    memset(&discordActivity, 0, sizeof(discordActivity));

    // Copy strings safely
    if (!activity.state.isEmpty()) {
        strncpy(discordActivity.state, activity.state.toUtf8().constData(),
                sizeof(discordActivity.state) - 1);
    }

    if (!activity.details.isEmpty()) {
        strncpy(discordActivity.details, activity.details.toUtf8().constData(),
                sizeof(discordActivity.details) - 1);
    }

    if (!activity.largeImageKey.isEmpty()) {
        strncpy(discordActivity.assets.large_image, activity.largeImageKey.toUtf8().constData(),
                sizeof(discordActivity.assets.large_image) - 1);
    }

    if (!activity.largeImageText.isEmpty()) {
        strncpy(discordActivity.assets.large_text, activity.largeImageText.toUtf8().constData(),
                sizeof(discordActivity.assets.large_text) - 1);
    }

    if (!activity.smallImageKey.isEmpty()) {
        strncpy(discordActivity.assets.small_image, activity.smallImageKey.toUtf8().constData(),
                sizeof(discordActivity.assets.small_image) - 1);
    }

    if (!activity.smallImageText.isEmpty()) {
        strncpy(discordActivity.assets.small_text, activity.smallImageText.toUtf8().constData(),
                sizeof(discordActivity.assets.small_text) - 1);
    }

    discordActivity.timestamps.start = activity.startTimestamp;
    discordActivity.timestamps.end = activity.endTimestamp;

    m_activityManager->update_activity(m_activityManager, &discordActivity, nullptr, nullptr);
    emit activityUpdated();
    */
}

void DiscordManager::clearActivity()
{
    // Temporarily disabled for testing
    qDebug() << "clearActivity called (disabled for testing)";
    emit activityUpdated();

    /*
    if (!m_activityManager) {
        qWarning() << "Activity manager not available";
        return;
    }

    m_activityManager->clear_activity(m_activityManager, nullptr, nullptr);
    emit activityUpdated();
    */
}

QString DiscordManager::getUsername() const
{
    if (!m_userManager) {
        return QString();
    }
    
    struct DiscordUser currentUser;
    enum EDiscordResult result = m_userManager->get_current_user(m_userManager, &currentUser);
    
    if (result == DiscordResult_Ok) {
        return QString::fromUtf8(currentUser.username);
    }
    
    return QString();
}

QString DiscordManager::getDiscriminator() const
{
    if (!m_userManager) {
        return QString();
    }
    
    struct DiscordUser currentUser;
    enum EDiscordResult result = m_userManager->get_current_user(m_userManager, &currentUser);
    
    if (result == DiscordResult_Ok) {
        return QString::fromUtf8(currentUser.discriminator);
    }
    
    return QString();
}

QString DiscordManager::getUserId() const
{
    if (!m_userManager) {
        return QString();
    }
    
    struct DiscordUser currentUser;
    enum EDiscordResult result = m_userManager->get_current_user(m_userManager, &currentUser);
    
    if (result == DiscordResult_Ok) {
        return QString::number(currentUser.id);
    }
    
    return QString();
}

void DiscordManager::runCallbacks()
{
    if (m_core) {
        enum EDiscordResult result = m_core->run_callbacks(m_core);
        if (result != DiscordResult_Ok) {
            qWarning() << "Discord callback error:" << result;
        }
    }
}

void DiscordManager::onCurrentUserUpdate(void* data)
{
    Q_UNUSED(data)
    // This callback is called when the current user is updated
    qDebug() << "Current user updated";
}

void DiscordManager::updateCurrentUser()
{
    // Update user information
    emit connected();
}


