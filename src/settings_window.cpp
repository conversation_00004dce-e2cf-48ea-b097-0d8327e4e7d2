#include "settings_window.h"
#include "resources.h"
#include <QApplication>
#include <QMessageBox>
#include <QCloseEvent>
#include <QDateTime>
#include <QDebug>

SettingsWindow::SettingsWindow(QWidget *parent)
    : QMainWindow(parent)
    , m_centralWidget(nullptr)
    , m_discordManager(new DiscordManager(this))
    , m_settings(new QSettings("DiscordRPC-ReGen", "Settings", this))
    , m_timestampTimer(new QTimer(this))
{
    setWindowTitle("Discord RPC Settings");
    setWindowIcon(Resources::getApplicationIcon());
    setMinimumSize(600, 500);
    resize(800, 600);
    
    setupUI();
    setupMenuBar();
    setupSystemTray();
    
    // Connect Discord Manager signals
    connect(m_discordManager, &DiscordManager::connected, 
            this, &SettingsWindow::onDiscordConnected);
    connect(m_discordManager, &DiscordManager::disconnected, 
            this, &SettingsWindow::onDiscordDisconnected);
    connect(m_discordManager, &DiscordManager::errorOccurred, 
            this, &SettingsWindow::onDiscordError);
    
    // Connect timestamp timer
    connect(m_timestampTimer, &QTimer::timeout, this, &SettingsWindow::updateTimestamp);
    m_timestampTimer->start(1000); // Update every second
    
    loadSettings();
    updateConnectionStatus();
}

SettingsWindow::~SettingsWindow()
{
    saveSettings();
}

void SettingsWindow::setupUI()
{
    m_centralWidget = new QWidget(this);
    setCentralWidget(m_centralWidget);
    
    m_mainLayout = new QVBoxLayout(m_centralWidget);
    
    // Connection Group
    m_connectionGroup = new QGroupBox("Discord Connection", this);
    QGridLayout* connectionLayout = new QGridLayout(m_connectionGroup);
    
    connectionLayout->addWidget(new QLabel("Application ID:"), 0, 0);
    m_applicationIdEdit = new QLineEdit(this);
    m_applicationIdEdit->setPlaceholderText("Enter your Discord Application ID");
    connectionLayout->addWidget(m_applicationIdEdit, 0, 1);
    
    m_connectButton = new QPushButton("Connect", this);
    m_disconnectButton = new QPushButton("Disconnect", this);
    m_disconnectButton->setEnabled(false);
    
    QHBoxLayout* connectionButtonLayout = new QHBoxLayout();
    connectionButtonLayout->addWidget(m_connectButton);
    connectionButtonLayout->addWidget(m_disconnectButton);
    connectionButtonLayout->addStretch();
    connectionLayout->addLayout(connectionButtonLayout, 1, 0, 1, 2);
    
    m_statusLabel = new QLabel("Status: Disconnected", this);
    m_statusLabel->setStyleSheet("QLabel { color: red; font-weight: bold; }");
    connectionLayout->addWidget(m_statusLabel, 2, 0, 1, 2);
    
    m_userInfoLabel = new QLabel("User: Not connected", this);
    connectionLayout->addWidget(m_userInfoLabel, 3, 0, 1, 2);
    
    m_mainLayout->addWidget(m_connectionGroup);
    
    // Activity Group
    m_activityGroup = new QGroupBox("Rich Presence Activity", this);
    QGridLayout* activityLayout = new QGridLayout(m_activityGroup);
    
    activityLayout->addWidget(new QLabel("State:"), 0, 0);
    m_stateEdit = new QLineEdit(this);
    m_stateEdit->setPlaceholderText("e.g., In Game, Browsing Menu");
    activityLayout->addWidget(m_stateEdit, 0, 1);
    
    activityLayout->addWidget(new QLabel("Details:"), 1, 0);
    m_detailsEdit = new QLineEdit(this);
    m_detailsEdit->setPlaceholderText("e.g., Playing Solo, Level 5");
    activityLayout->addWidget(m_detailsEdit, 1, 1);
    
    activityLayout->addWidget(new QLabel("Large Image Key:"), 2, 0);
    m_largeImageKeyEdit = new QLineEdit(this);
    m_largeImageKeyEdit->setPlaceholderText("Asset key for large image");
    activityLayout->addWidget(m_largeImageKeyEdit, 2, 1);
    
    activityLayout->addWidget(new QLabel("Large Image Text:"), 3, 0);
    m_largeImageTextEdit = new QLineEdit(this);
    m_largeImageTextEdit->setPlaceholderText("Hover text for large image");
    activityLayout->addWidget(m_largeImageTextEdit, 3, 1);
    
    activityLayout->addWidget(new QLabel("Small Image Key:"), 4, 0);
    m_smallImageKeyEdit = new QLineEdit(this);
    m_smallImageKeyEdit->setPlaceholderText("Asset key for small image");
    activityLayout->addWidget(m_smallImageKeyEdit, 4, 1);
    
    activityLayout->addWidget(new QLabel("Small Image Text:"), 5, 0);
    m_smallImageTextEdit = new QLineEdit(this);
    m_smallImageTextEdit->setPlaceholderText("Hover text for small image");
    activityLayout->addWidget(m_smallImageTextEdit, 5, 1);
    
    // Timestamp settings
    m_enableTimestampCheck = new QCheckBox("Enable Timestamp", this);
    activityLayout->addWidget(m_enableTimestampCheck, 6, 0, 1, 2);
    
    activityLayout->addWidget(new QLabel("Timestamp Type:"), 7, 0);
    m_timestampTypeCombo = new QComboBox(this);
    m_timestampTypeCombo->addItems({"Start Time (Elapsed)", "End Time (Remaining)", "Custom Time"});
    activityLayout->addWidget(m_timestampTypeCombo, 7, 1);
    
    activityLayout->addWidget(new QLabel("Custom Time:"), 8, 0);
    m_customTimeEdit = new QDateTimeEdit(QDateTime::currentDateTime(), this);
    m_customTimeEdit->setEnabled(false);
    activityLayout->addWidget(m_customTimeEdit, 8, 1);
    
    // Activity buttons
    m_updateActivityButton = new QPushButton("Update Activity", this);
    m_clearActivityButton = new QPushButton("Clear Activity", this);
    
    QHBoxLayout* activityButtonLayout = new QHBoxLayout();
    activityButtonLayout->addWidget(m_updateActivityButton);
    activityButtonLayout->addWidget(m_clearActivityButton);
    activityButtonLayout->addStretch();
    activityLayout->addLayout(activityButtonLayout, 9, 0, 1, 2);
    
    m_mainLayout->addWidget(m_activityGroup);
    
    // Control Buttons
    m_buttonLayout = new QHBoxLayout();
    m_saveButton = new QPushButton("Save Settings", this);
    m_loadButton = new QPushButton("Load Settings", this);
    m_resetButton = new QPushButton("Reset to Defaults", this);
    
    m_buttonLayout->addWidget(m_saveButton);
    m_buttonLayout->addWidget(m_loadButton);
    m_buttonLayout->addWidget(m_resetButton);
    m_buttonLayout->addStretch();
    
    m_mainLayout->addLayout(m_buttonLayout);
    m_mainLayout->addStretch();
    
    // Connect signals
    connect(m_connectButton, &QPushButton::clicked, this, &SettingsWindow::connectToDiscord);
    connect(m_disconnectButton, &QPushButton::clicked, this, &SettingsWindow::disconnectFromDiscord);
    connect(m_updateActivityButton, &QPushButton::clicked, this, &SettingsWindow::updateActivity);
    connect(m_clearActivityButton, &QPushButton::clicked, this, &SettingsWindow::clearActivity);
    connect(m_saveButton, &QPushButton::clicked, this, &SettingsWindow::saveSettings);
    connect(m_loadButton, &QPushButton::clicked, this, &SettingsWindow::loadSettings);
    connect(m_resetButton, &QPushButton::clicked, this, &SettingsWindow::resetSettings);
    
    connect(m_timestampTypeCombo, QOverload<int>::of(&QComboBox::currentIndexChanged),
            [this](int index) {
                m_customTimeEdit->setEnabled(index == 2); // Custom Time
            });
}

void SettingsWindow::setupMenuBar()
{
    QMenuBar* menuBar = this->menuBar();

    // File Menu
    QMenu* fileMenu = menuBar->addMenu("&File");

    QAction* saveAction = fileMenu->addAction("&Save Settings");
    saveAction->setShortcut(QKeySequence::Save);
    connect(saveAction, &QAction::triggered, this, &SettingsWindow::saveSettings);

    QAction* loadAction = fileMenu->addAction("&Load Settings");
    loadAction->setShortcut(QKeySequence::Open);
    connect(loadAction, &QAction::triggered, this, &SettingsWindow::loadSettings);

    fileMenu->addSeparator();

    QAction* exitAction = fileMenu->addAction("E&xit");
    exitAction->setShortcut(QKeySequence::Quit);
    connect(exitAction, &QAction::triggered, this, &QWidget::close);

    // Help Menu
    QMenu* helpMenu = menuBar->addMenu("&Help");

    QAction* aboutAction = helpMenu->addAction("&About");
    connect(aboutAction, &QAction::triggered, this, &SettingsWindow::showAbout);
}

void SettingsWindow::setupSystemTray()
{
    if (!QSystemTrayIcon::isSystemTrayAvailable()) {
        qWarning() << "System tray is not available";
        return;
    }

    m_trayIcon = new QSystemTrayIcon(this);
    // Use embedded icon
    m_trayIcon->setIcon(Resources::getApplicationIcon());
    m_trayIcon->setToolTip("Discord RPC Settings");

    m_trayMenu = new QMenu(this);

    m_showAction = m_trayMenu->addAction("Show Window");
    connect(m_showAction, &QAction::triggered, this, &SettingsWindow::toggleWindow);

    m_trayMenu->addSeparator();

    m_quitAction = m_trayMenu->addAction("Quit");
    connect(m_quitAction, &QAction::triggered, qApp, &QApplication::quit);

    m_trayIcon->setContextMenu(m_trayMenu);

    connect(m_trayIcon, &QSystemTrayIcon::activated,
            this, &SettingsWindow::onTrayIconActivated);

    m_trayIcon->show();
}

void SettingsWindow::connectToDiscord()
{
    QString applicationId = m_applicationIdEdit->text().trimmed();
    if (applicationId.isEmpty()) {
        QMessageBox::warning(this, "Error", "Please enter a valid Application ID");
        return;
    }

    bool ok;
    int64_t appId = applicationId.toLongLong(&ok);
    if (!ok) {
        QMessageBox::warning(this, "Error", "Application ID must be a valid number");
        return;
    }

    if (m_discordManager->initialize(appId)) {
        m_connectButton->setEnabled(false);
        m_disconnectButton->setEnabled(true);
        m_applicationIdEdit->setEnabled(false);
        updateConnectionStatus();
    } else {
        QMessageBox::critical(this, "Connection Error",
                             "Failed to connect to Discord. Make sure Discord is running.");
    }
}

void SettingsWindow::disconnectFromDiscord()
{
    m_discordManager->shutdown();
    m_connectButton->setEnabled(true);
    m_disconnectButton->setEnabled(false);
    m_applicationIdEdit->setEnabled(true);
    updateConnectionStatus();
}

void SettingsWindow::updateActivity()
{
    if (!m_discordManager->isConnected()) {
        QMessageBox::warning(this, "Error", "Not connected to Discord");
        return;
    }

    DiscordManager::ActivityData activity;
    activity.state = m_stateEdit->text();
    activity.details = m_detailsEdit->text();
    activity.largeImageKey = m_largeImageKeyEdit->text();
    activity.largeImageText = m_largeImageTextEdit->text();
    activity.smallImageKey = m_smallImageKeyEdit->text();
    activity.smallImageText = m_smallImageTextEdit->text();

    if (m_enableTimestampCheck->isChecked()) {
        int timestampType = m_timestampTypeCombo->currentIndex();
        QDateTime currentTime = QDateTime::currentDateTime();

        switch (timestampType) {
        case 0: // Start Time (Elapsed)
            activity.startTimestamp = currentTime.toSecsSinceEpoch();
            break;
        case 1: // End Time (Remaining) - 1 hour from now as example
            activity.endTimestamp = currentTime.addSecs(3600).toSecsSinceEpoch();
            break;
        case 2: // Custom Time
            activity.startTimestamp = m_customTimeEdit->dateTime().toSecsSinceEpoch();
            break;
        }
    }

    m_discordManager->setActivity(activity);
    statusBar()->showMessage("Activity updated", 2000);
}

void SettingsWindow::clearActivity()
{
    if (!m_discordManager->isConnected()) {
        QMessageBox::warning(this, "Error", "Not connected to Discord");
        return;
    }

    m_discordManager->clearActivity();
    statusBar()->showMessage("Activity cleared", 2000);
}

void SettingsWindow::onDiscordConnected()
{
    updateConnectionStatus();
    updateUserInfo();
    statusBar()->showMessage("Connected to Discord", 3000);
}

void SettingsWindow::onDiscordDisconnected()
{
    updateConnectionStatus();
    statusBar()->showMessage("Disconnected from Discord", 3000);
}

void SettingsWindow::onDiscordError(const QString& error)
{
    QMessageBox::critical(this, "Discord Error", error);
    statusBar()->showMessage("Discord Error: " + error, 5000);
}

void SettingsWindow::loadSettings()
{
    m_applicationIdEdit->setText(m_settings->value("connection/applicationId", "").toString());
    m_stateEdit->setText(m_settings->value("activity/state", "").toString());
    m_detailsEdit->setText(m_settings->value("activity/details", "").toString());
    m_largeImageKeyEdit->setText(m_settings->value("activity/largeImageKey", "").toString());
    m_largeImageTextEdit->setText(m_settings->value("activity/largeImageText", "").toString());
    m_smallImageKeyEdit->setText(m_settings->value("activity/smallImageKey", "").toString());
    m_smallImageTextEdit->setText(m_settings->value("activity/smallImageText", "").toString());
    m_enableTimestampCheck->setChecked(m_settings->value("activity/enableTimestamp", false).toBool());
    m_timestampTypeCombo->setCurrentIndex(m_settings->value("activity/timestampType", 0).toInt());

    // Restore window geometry
    restoreGeometry(m_settings->value("window/geometry").toByteArray());
    restoreState(m_settings->value("window/state").toByteArray());

    statusBar()->showMessage("Settings loaded", 2000);
}

void SettingsWindow::saveSettings()
{
    m_settings->setValue("connection/applicationId", m_applicationIdEdit->text());
    m_settings->setValue("activity/state", m_stateEdit->text());
    m_settings->setValue("activity/details", m_detailsEdit->text());
    m_settings->setValue("activity/largeImageKey", m_largeImageKeyEdit->text());
    m_settings->setValue("activity/largeImageText", m_largeImageTextEdit->text());
    m_settings->setValue("activity/smallImageKey", m_smallImageKeyEdit->text());
    m_settings->setValue("activity/smallImageText", m_smallImageTextEdit->text());
    m_settings->setValue("activity/enableTimestamp", m_enableTimestampCheck->isChecked());
    m_settings->setValue("activity/timestampType", m_timestampTypeCombo->currentIndex());

    // Save window geometry
    m_settings->setValue("window/geometry", saveGeometry());
    m_settings->setValue("window/state", saveState());

    m_settings->sync();
    statusBar()->showMessage("Settings saved", 2000);
}

void SettingsWindow::resetSettings()
{
    int ret = QMessageBox::question(this, "Reset Settings",
                                   "Are you sure you want to reset all settings to defaults?",
                                   QMessageBox::Yes | QMessageBox::No);

    if (ret == QMessageBox::Yes) {
        m_settings->clear();

        // Reset UI to defaults
        m_applicationIdEdit->clear();
        m_stateEdit->clear();
        m_detailsEdit->clear();
        m_largeImageKeyEdit->clear();
        m_largeImageTextEdit->clear();
        m_smallImageKeyEdit->clear();
        m_smallImageTextEdit->clear();
        m_enableTimestampCheck->setChecked(false);
        m_timestampTypeCombo->setCurrentIndex(0);
        m_customTimeEdit->setDateTime(QDateTime::currentDateTime());

        statusBar()->showMessage("Settings reset to defaults", 2000);
    }
}

void SettingsWindow::showAbout()
{
    QMessageBox::about(this, "About Discord RPC Settings",
                      "<h3>Discord RPC Settings</h3>"
                      "<p>A Qt6-based application for managing Discord Rich Presence.</p>"
                      "<p>Built with Discord Game SDK and Qt6.</p>"
                      "<p>Version 1.0</p>");
}

void SettingsWindow::toggleWindow()
{
    if (isVisible()) {
        hide();
        m_showAction->setText("Show Window");
    } else {
        show();
        raise();
        activateWindow();
        m_showAction->setText("Hide Window");
    }
}

void SettingsWindow::onTrayIconActivated(QSystemTrayIcon::ActivationReason reason)
{
    if (reason == QSystemTrayIcon::DoubleClick) {
        toggleWindow();
    }
}

void SettingsWindow::updateTimestamp()
{
    // Update custom time to current time if needed
    if (m_timestampTypeCombo->currentIndex() == 2 && !m_customTimeEdit->hasFocus()) {
        // Only update if user isn't actively editing
    }
}

void SettingsWindow::updateConnectionStatus()
{
    if (m_discordManager->isConnected()) {
        m_statusLabel->setText("Status: Connected");
        m_statusLabel->setStyleSheet("QLabel { color: green; font-weight: bold; }");
    } else {
        m_statusLabel->setText("Status: Disconnected");
        m_statusLabel->setStyleSheet("QLabel { color: red; font-weight: bold; }");
        m_userInfoLabel->setText("User: Not connected");
    }
}

void SettingsWindow::updateUserInfo()
{
    if (m_discordManager->isConnected()) {
        QString username = m_discordManager->getUsername();
        QString discriminator = m_discordManager->getDiscriminator();
        QString userId = m_discordManager->getUserId();

        if (!username.isEmpty()) {
            m_userInfoLabel->setText(QString("User: %1#%2 (ID: %3)")
                                   .arg(username, discriminator, userId));
        }
    }
}

void SettingsWindow::closeEvent(QCloseEvent *event)
{
    if (m_trayIcon && m_trayIcon->isVisible()) {
        hide();
        event->ignore();
        m_trayIcon->showMessage("Discord RPC Settings",
                               "Application was minimized to tray",
                               QSystemTrayIcon::Information, 2000);
    } else {
        saveSettings();
        event->accept();
    }
}


