#ifndef SETTINGS_WINDOW_H
#define SETTINGS_WINDOW_H

#include <QMainWindow>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QGroupBox>
#include <QLabel>
#include <QLineEdit>
#include <QPushButton>
#include <QSpinBox>
#include <QTextEdit>
#include <QCheckBox>
#include <QComboBox>
#include <QDateTimeEdit>
#include <QStatusBar>
#include <QMenuBar>
#include <QAction>
#include <QSettings>
#include <QTimer>
#include <QSystemTrayIcon>
#include <QMenu>

#include "discord_manager.h"

class SettingsWindow : public QMainWindow
{
    Q_OBJECT

public:
    SettingsWindow(QWidget *parent = nullptr);
    ~SettingsWindow();

protected:
    void closeEvent(QCloseEvent *event) override;

private slots:
    void connectToDiscord();
    void disconnectFromDiscord();
    void updateActivity();
    void clearActivity();
    void onDiscordConnected();
    void onDiscordDisconnected();
    void onDiscordError(const QString& error);
    void loadSettings();
    void saveSettings();
    void resetSettings();
    void showAbout();
    void toggleWindow();
    void onTrayIconActivated(QSystemTrayIcon::ActivationReason reason);
    void updateTimestamp();

private:
    void setupUI();
    void setupMenuBar();
    void setupSystemTray();
    void updateConnectionStatus();
    void updateUserInfo();
    
    // UI Components
    QWidget* m_centralWidget;
    QVBoxLayout* m_mainLayout;
    
    // Connection Group
    QGroupBox* m_connectionGroup;
    QLineEdit* m_applicationIdEdit;
    QPushButton* m_connectButton;
    QPushButton* m_disconnectButton;
    QLabel* m_statusLabel;
    QLabel* m_userInfoLabel;
    
    // Activity Group
    QGroupBox* m_activityGroup;
    QLineEdit* m_stateEdit;
    QLineEdit* m_detailsEdit;
    QLineEdit* m_largeImageKeyEdit;
    QLineEdit* m_largeImageTextEdit;
    QLineEdit* m_smallImageKeyEdit;
    QLineEdit* m_smallImageTextEdit;
    QCheckBox* m_enableTimestampCheck;
    QComboBox* m_timestampTypeCombo;
    QDateTimeEdit* m_customTimeEdit;
    QPushButton* m_updateActivityButton;
    QPushButton* m_clearActivityButton;
    
    // Control Buttons
    QHBoxLayout* m_buttonLayout;
    QPushButton* m_saveButton;
    QPushButton* m_loadButton;
    QPushButton* m_resetButton;
    
    // System Tray
    QSystemTrayIcon* m_trayIcon;
    QMenu* m_trayMenu;
    QAction* m_showAction;
    QAction* m_quitAction;
    
    // Discord Manager
    DiscordManager* m_discordManager;
    
    // Settings
    QSettings* m_settings;
    
    // Timer for timestamp updates
    QTimer* m_timestampTimer;
};

#endif // SETTINGS_WINDOW_H
