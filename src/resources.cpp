#include "resources.h"
#include <QDebug>

QIcon Resources::getApplicationIcon()
{
    return QIcon(getApplicationPixmap());
}

QPixmap Resources::getApplicationPixmap()
{
    // Load pixmap from embedded data
    QPixmap pixmap;
    
    // Try to load from embedded data
    if (icon_png_len > 0) {
        if (pixmap.loadFromData(icon_png, icon_png_len, "PNG")) {
            qDebug() << "Loaded embedded icon successfully, size:" << pixmap.size();
            return pixmap;
        } else {
            qWarning() << "Failed to load embedded icon data";
        }
    } else {
        qWarning() << "No embedded icon data found";
    }
    
    // Fallback: create a simple colored pixmap
    qDebug() << "Using fallback icon";
    pixmap = QPixmap(32, 32);
    pixmap.fill(QColor(88, 101, 242)); // Discord's brand color
    
    return pixmap;
}
