#ifndef RESOURCES_H
#define RESOURCES_H

#include <QPixmap>
#include <QIcon>

// Forward declarations for embedded resources
extern const unsigned char icon_png[];
extern const unsigned int icon_png_len;

class Resources
{
public:
    // Get the application icon as QIcon
    static QIcon getApplicationIcon();
    
    // Get the application icon as QPixmap
    static QPixmap getApplicationPixmap();

private:
    Resources() = delete; // Static class
};

#endif // RESOURCES_H
