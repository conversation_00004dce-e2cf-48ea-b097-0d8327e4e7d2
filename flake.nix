{
  description = "Discord RPC Settings - Qt6 application with Discord Game SDK";

  inputs = {
    nixpkgs.url = "github:NixOS/nixpkgs/nixos-unstable";
    flake-utils.url = "github:numtide/flake-utils";
  };

  outputs = { self, nixpkgs, flake-utils }:
    flake-utils.lib.eachDefaultSystem (system:
      let
        pkgs = nixpkgs.legacyPackages.${system};
        
        # Custom xmake package with Qt6 support
        xmake-qt = pkgs.xmake.overrideAttrs (oldAttrs: {
          buildInputs = oldAttrs.buildInputs ++ [ pkgs.qt6.full ];
        });

      in
      {
        devShells.default = pkgs.mkShell {
          buildInputs = with pkgs; [
            # Build tools
            xmake-qt
            cmake
            ninja
            pkg-config
            
            # Qt6 development
            qt6.full
            qt6.qtbase
            qt6.qttools
            qt6.wrapQtAppsHook
            
            # C++ development
            gcc
            gdb
            clang-tools
            
            # Additional libraries that might be needed
            libGL
            libxkbcommon
            wayland
            xorg.libX11
            xorg.libXcursor
            xorg.libXrandr
            xorg.libXi
            fontconfig
            freetype
            
            # Development utilities
            git
            vim
            
            # For system tray functionality
            libnotify
            
            # Runtime dependencies
            discord
          ];

          shellHook = ''
            echo "Discord RPC Settings Development Environment"
            echo "==========================================="
            echo "Available tools:"
            echo "  - xmake: Build system"
            echo "  - Qt6: GUI framework"
            echo "  - Discord Game SDK: Located in third_party/"
            echo ""
            echo "Quick start:"
            echo "  xmake f -p linux -a x86_64 -m debug"
            echo "  xmake"
            echo "  xmake run"
            echo ""
            
            # Set up Qt6 environment
            export QT_QPA_PLATFORM_PLUGIN_PATH="${pkgs.qt6.qtbase}/lib/qt-6/plugins"
            export QML2_IMPORT_PATH="${pkgs.qt6.qtdeclarative}/lib/qt-6/qml"
            
            # Ensure Discord Game SDK library can be found
            export LD_LIBRARY_PATH="$PWD/third_party/discord_game_sdk/lib/x86_64:$LD_LIBRARY_PATH"
            
            # Set up xmake
            export XMAKE_ROOT="$HOME/.xmake"
            
            # Make sure we can find Qt6
            export PKG_CONFIG_PATH="${pkgs.qt6.qtbase}/lib/pkgconfig:$PKG_CONFIG_PATH"
            export CMAKE_PREFIX_PATH="${pkgs.qt6.qtbase}:$CMAKE_PREFIX_PATH"
          '';

          # Qt6 specific environment variables
          QT_QPA_PLATFORM_PLUGIN_PATH = "${pkgs.qt6.qtbase}/lib/qt-6/plugins";
          QML2_IMPORT_PATH = "${pkgs.qt6.qtdeclarative}/lib/qt-6/qml";
        };

        packages.default = pkgs.stdenv.mkDerivation {
          pname = "discord-rpc-settings";
          version = "1.0.0";

          src = ./.;

          nativeBuildInputs = with pkgs; [
            xmake-qt
            qt6.wrapQtAppsHook
            pkg-config
          ];

          buildInputs = with pkgs; [
            qt6.qtbase
            qt6.qttools
            libGL
            libxkbcommon
            wayland
            xorg.libX11
            fontconfig
            freetype
          ];

          configurePhase = ''
            export HOME=$TMPDIR
            xmake f -p linux -a x86_64 -m release --qt=${pkgs.qt6.qtbase}
          '';

          buildPhase = ''
            xmake
          '';

          installPhase = ''
            mkdir -p $out/bin
            mkdir -p $out/lib
            
            # Install the binary
            cp build/linux/x86_64/release/DiscordRPC-ReGen $out/bin/
            
            # Install Discord Game SDK library
            cp third_party/discord_game_sdk/lib/x86_64/discord_game_sdk.so $out/lib/
            
            # Wrap the binary to include the library path
            wrapProgram $out/bin/DiscordRPC-ReGen \
              --prefix LD_LIBRARY_PATH : $out/lib
          '';

          meta = with pkgs.lib; {
            description = "Qt6-based Discord Rich Presence settings application";
            homepage = "https://github.com/your-username/DiscordRPC-ReGen";
            license = licenses.mit;
            platforms = platforms.linux;
            maintainers = [ ];
          };
        };
      });
}
